// API Client Service - Centralized API communication
class ApiClient {
    constructor() {
        this.baseUrl = '/api';
    }

    /**
     * Make a generic API request
     */
    async makeRequest(url, options = {}) {
        try {
            // Only set Content-Type header if there's a body
            const headers = { ...options.headers };
            if (options.body) {
                headers['Content-Type'] = 'application/json';
            }

            const response = await fetch(url, {
                headers,
                ...options
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}: ${response.statusText}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Domain API methods
    async getDomains() {
        return this.makeRequest(`${this.baseUrl}/domains`);
    }

    async createDomain(domainData) {
        return this.makeRequest(`${this.baseUrl}/domains`, {
            method: 'POST',
            body: JSON.stringify(domainData)
        });
    }

    async updateDomainStatus(domainId, isActive) {
        return this.makeRequest(`${this.baseUrl}/domains/${domainId}/status`, {
            method: 'PUT',
            body: JSON.stringify({ active: isActive })
        });
    }

    async updateDomainWebhook(domainId, webhookId) {
        return this.makeRequest(`${this.baseUrl}/domains/${domainId}/webhook`, {
            method: 'PUT',
            body: JSON.stringify({ webhookId })
        });
    }

    async deleteDomain(domainName) {
        return this.makeRequest(`${this.baseUrl}/domains/${domainName}`, {
            method: 'DELETE'
        });
    }

    async verifyDomain(domainName) {
        return this.makeRequest(`${this.baseUrl}/domains/${domainName}/verify`, {
            method: 'POST'
        });
    }

    // Webhook API methods
    async getWebhooks() {
        return this.makeRequest(`${this.baseUrl}/webhooks`);
    }

    async createWebhook(webhookData) {
        return this.makeRequest(`${this.baseUrl}/webhooks`, {
            method: 'POST',
            body: JSON.stringify(webhookData)
        });
    }

    async updateWebhook(webhookId, updates) {
        return this.makeRequest(`${this.baseUrl}/webhooks/${webhookId}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    }

    async deleteWebhook(webhookId) {
        return this.makeRequest(`${this.baseUrl}/webhooks/${webhookId}`, {
            method: 'DELETE'
        });
    }

    async verifyWebhook(webhookId) {
        return this.makeRequest(`${this.baseUrl}/config/webhooks/${webhookId}/verify`, {
            method: 'POST'
        });
    }

    // Alias API methods
    async getAliases() {
        return this.makeRequest(`${this.baseUrl}/config/aliases`);
    }

    async createAlias(aliasData) {
        return this.makeRequest(`${this.baseUrl}/config/aliases`, {
            method: 'POST',
            body: JSON.stringify(aliasData)
        });
    }

    async updateAlias(aliasId, updates) {
        return this.makeRequest(`${this.baseUrl}/config/aliases/${aliasId}`, {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    }

    async deleteAlias(aliasId) {
        return this.makeRequest(`${this.baseUrl}/config/aliases/${aliasId}`, {
            method: 'DELETE'
        });
    }

    async getDomainAliases(domainId) {
        return this.makeRequest(`${this.baseUrl}/domains/${domainId}/aliases`);
    }

    // Dashboard API methods
    async getMetrics() {
        return this.makeRequest(`${this.baseUrl}/dashboard/metrics`);
    }

    async getLogs(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.makeRequest(`${this.baseUrl}/dashboard/logs?${params}`);
    }
}

// Export as singleton
window.apiClient = new ApiClient();
export default window.apiClient;
