// Refactored User Dashboard - Main Entry Point
// This file imports and orchestrates all the modular components

import apiClient from './services/api-client.js';
// Toast, confirm dialogs, and table sorting now handled by Vue
// import toast from './modules/ui/toast-notifications.js';
// import confirmDialog from './modules/ui/confirmation-dialogs.js';
// import TableSorter from './modules/dashboard/table-sorter.js';
import TabManager from './modules/dashboard/tab-manager.js';

import formValidation from './modules/forms/validation.js';

class UserDashboard {
    constructor() {
        this.apiClient = apiClient;
        // Toast and confirm dialogs now handled by Vue
        this.toast = window.toast;
        this.confirmDialog = window.confirmDialog;
        this.eventListeners = []; // Track event listeners for cleanup
        this.init();
    }

    async init() {
        // Initialize remaining modules
        this.tabManager = new TabManager();
        // Table sorting now handled by Vue
        // this.tableSorter = new TableSorter();

        // Setup dashboard functionality
        this.setupDomainToggles();
        this.setupAliasToggles();
        this.setupActionButtons();

        // Make modules available globally for debugging
        window.tabManager = this.tabManager;
        // window.tableSorter = this.tableSorter;

        // Make dashboard functions available globally
        window.deleteAlias = this.deleteAlias.bind(this);
        window.deleteWebhook = this.deleteWebhook.bind(this);
        window.verifyWebhook = this.verifyWebhook.bind(this);
    }

    // Domain Status Toggle Functionality
    setupDomainToggles() {
        const toggles = document.querySelectorAll('.domain-status-toggle');
        
        toggles.forEach(toggle => {
            // Check if domain is verified
            const isVerified = toggle.getAttribute('data-verified') === 'true';
            const verificationStatus = toggle.getAttribute('data-verification-status');

            if (!isVerified && verificationStatus === 'PENDING') {
                const label = toggle.closest('label');
                label.title = 'Domain must be verified before it can be activated';

                const clickHandler = (e) => {
                    e.preventDefault();
                    this.toast.warning('Domain must be verified before it can be activated');
                };
                toggle.addEventListener('click', clickHandler);
                this.eventListeners.push({ element: toggle, event: 'click', handler: clickHandler });

                return;
            }
            
            // Set up change handler only for verified domains
            const changeHandler = async (e) => {
                const domainId = e.target.getAttribute('data-domain-id');
                const isActive = e.target.checked;
                
                // Optimistic UI update
                this.setToggleLoading(e.target, true);
                
                try {
                    const result = await this.apiClient.updateDomainStatus(domainId, isActive);
                    
                    if (result.success) {
                        this.toast.success(`Domain ${isActive ? 'activated' : 'deactivated'} successfully`);
                    } else {
                        // Revert toggle on failure
                        e.target.checked = !isActive;
                        this.toast.error('Failed to update domain status');
                    }
                } catch (error) {
                    console.error('Domain status update failed:', error);
                    // Revert toggle on error
                    e.target.checked = !isActive;
                    this.toast.error('Network error: Could not update domain status');
                } finally {
                    this.setToggleLoading(e.target, false);
                }
            };
            toggle.addEventListener('change', changeHandler);
            this.eventListeners.push({ element: toggle, event: 'change', handler: changeHandler });
        });
    }

    // Alias Status Toggle Functionality
    setupAliasToggles() {
        const toggles = document.querySelectorAll('.alias-status-toggle');

        toggles.forEach(toggle => {
            const changeHandler = async (e) => {
                const aliasId = e.target.getAttribute('data-alias-id');
                const isActive = e.target.checked;

                // Optimistic UI update
                this.setToggleLoading(e.target, true);

                try {
                    const result = await this.apiClient.updateAlias(aliasId, { active: isActive });

                    if (result.success) {
                        this.toast.success(`Alias ${isActive ? 'activated' : 'deactivated'} successfully`);
                    } else {
                        // Revert toggle on failure
                        e.target.checked = !isActive;
                        this.toast.error('Failed to update alias status');
                    }
                } catch (error) {
                    console.error('Alias status update failed:', error);
                    // Revert toggle on error
                    e.target.checked = !isActive;
                    this.toast.error('Network error: Could not update alias status');
                } finally {
                    this.setToggleLoading(e.target, false);
                }
            };
            toggle.addEventListener('change', changeHandler);
            this.eventListeners.push({ element: toggle, event: 'change', handler: changeHandler });
        });
    }

    // Visual loading state for toggles
    setToggleLoading(toggle, isLoading) {
        const toggleWrapper = toggle.nextElementSibling; // The visual toggle div
        const tableRow = toggle.closest('tr');
        
        if (isLoading) {
            toggleWrapper.classList.add('toggle-loading');
            tableRow.classList.add('row-loading');
            toggle.disabled = true;
        } else {
            toggleWrapper.classList.remove('toggle-loading');
            tableRow.classList.remove('row-loading');
            toggle.disabled = false;
        }
    }

    setupActionButtons() {
        // Make action functions available globally for inline handlers
        // Note: viewLogs is now handled by Vue components (DomainsView.vue, AliasesView.vue)
        // Don't override it here to allow Vue components to provide the implementation

        window.deleteDomain = async (domainId, domainName) => {
            const confirmed = await this.confirmDialog.confirmDelete(domainName, 'domain');
            if (confirmed) {
                await this.performDomainDelete(domainId, domainName);
            }
        };

        window.showAliases = (domainId) => {
            console.log(`Show aliases for domain: ${domainId}`);
            window.openModal('view-aliases');
        };

        window.verifyDomain = async (domainId) => {
            try {
                this.toast.info('Verifying domain...');
                const response = await this.apiClient.verifyDomain(domainId);

                if (response.success) {
                    this.toast.success('Domain verification successful!');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    this.toast.error('Domain verification failed: ' + (response.error || 'Unknown error'));
                }
            } catch (error) {
                console.error('Domain verification error:', error);
                this.toast.error('Network error during verification');
            }
        };
    }

    // Perform domain deletion
    async performDomainDelete(domainId, domainName) {
        try {
            // Show loading state
            this.toast.info('Deleting domain...');
            
            const result = await this.apiClient.deleteDomain(domainName);

            if (result.success) {
                this.toast.success(`Domain "${domainName}" deleted successfully`);
                
                // Remove the row from the table with animation
                this.removeTableRow(domainId);
                
                // Reload page after a short delay to update counts
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } catch (error) {
            console.error('Delete domain error:', error);
            this.toast.error(`Failed to delete domain: ${error.message}`);
        }
    }

    // Remove table row with animation
    removeTableRow(domainId) {
        const toggleElement = document.querySelector(`[data-domain-id="${domainId}"]`);
        if (toggleElement) {
            const row = toggleElement.closest('tr');
            if (row) {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-20px)';
                
                setTimeout(() => {
                    row.remove();
                }, 300);
            }
        }
    }

    // Legacy modal functions for backward compatibility
    openModal(modalId) {
        // Convert old modal IDs to new system
        const modalTypeMap = {
            'create-domain-modal': 'create-domain',
            'create-webhook-modal': 'create-webhook',
            'create-alias-modal': 'create-alias'
        };

        const newModalType = modalTypeMap[modalId] || modalId;
        if (window.openModal) {
            window.openModal(newModalType);
        }
    }

    closeModal(modalId) {
        // Use Vue modal system
        if (window.closeModal) {
            window.closeModal();
        }
    }

    // Utility methods
    getActiveTab() {
        return this.tabManager.getActiveTab();
    }

    refreshMetrics() {
        // Call the global loadMetrics function from metrics-pill.hbs
        if (window.loadMetrics) {
            return window.loadMetrics();
        }
    }

    resetTableSort() {
        // Table sorting now handled by Vue
        console.log('Table sorting is now handled by Vue components');
    }

    // Alias management functions
    async deleteAlias(aliasId, aliasEmail) {
        const confirmed = await this.confirmDialog.confirmDelete(aliasEmail, 'alias');
        if (confirmed) {
            await this.performAliasDelete(aliasId, aliasEmail);
        }
    }

    async performAliasDelete(aliasId, aliasEmail) {
        try {
            this.toast.info('Deleting alias...');

            const response = await fetch(`/api/aliases/${aliasId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.toast.success(`Alias "${aliasEmail}" deleted successfully`);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                const error = await response.json();
                this.toast.error('Failed to delete alias: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Delete alias error:', error);
            this.toast.error(`Failed to delete alias: ${error.message}`);
        }
    }

    // Webhook management functions
    async deleteWebhook(webhookId, webhookName) {
        const confirmed = await this.confirmDialog.confirmDelete(webhookName, 'webhook');
        if (confirmed) {
            await this.performWebhookDelete(webhookId, webhookName);
        }
    }

    async performWebhookDelete(webhookId, webhookName) {
        try {
            this.toast.info('Deleting webhook...');

            const response = await fetch(`/api/webhooks/${webhookId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.toast.success(`Webhook "${webhookName}" deleted successfully`);
                setTimeout(() => window.location.reload(), 1500);
            } else {
                const error = await response.json();
                this.toast.error('Failed to delete webhook: ' + (error.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Delete webhook error:', error);
            this.toast.error(`Failed to delete webhook: ${error.message}`);
        }
    }

    async verifyWebhook(webhookId) {
        try {
            this.toast.info('Sending webhook verification request...');

            const response = await fetch(`/api/webhooks/${webhookId}/verify`, {
                method: 'POST'
            });

            if (response.ok) {
                const result = await response.json();
                this.toast.success('Verification request sent! Check your webhook for the token.');
                // Don't reload the page - let the user enter the token in the modal
                return result;
            } else {
                const error = await response.json();
                this.toast.error('Webhook verification failed: ' + (error.message || 'Unknown error'));
                throw new Error(error.message || 'Webhook verification failed');
            }
        } catch (error) {
            console.error('Verify webhook error:', error);
            this.toast.error(`Failed to verify webhook: ${error.message}`);
            throw error;
        }
    }

    // Cleanup method to prevent memory leaks
    cleanup() {
        // Remove all tracked event listeners
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners = [];

        // Clear any global references
        if (window.dashboard === this) {
            window.dashboard = null;
        }
    }
}

// Global modal functions are now handled by Vue in DashboardApp.vue
// This file no longer needs to define them

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new UserDashboard();
});

// Cleanup on page unload to prevent memory leaks
window.addEventListener('beforeunload', () => {
    if (window.dashboard && typeof window.dashboard.cleanup === 'function') {
        window.dashboard.cleanup();
    }
});

// Make UserDashboard available globally
window.UserDashboard = UserDashboard;

export default UserDashboard;
