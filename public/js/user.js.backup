// User Dashboard JavaScript Functionality

class UserDashboard {
    constructor() {
        this.activeTab = this.getActiveTabFromURL();
        this.init();
    }

    init() {
        this.setupDropdowns();
        this.setupTabs();
        this.loadMetrics();
        this.updateCreateButton();
        this.setupDomainToggles();
        this.setupActionButtons();
        this.setupTableSorting();
    }

    // Get active tab from URL parameter
    getActiveTabFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('tab') || 'domains';
    }

    // Tab Management
    setupTabs() {
        const tabLinks = document.querySelectorAll('.tab-link');
        
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = link.getAttribute('data-tab');
                this.switchToTab(tab);
            });
        });
    }

    switchToTab(tab) {
        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('tab', tab);
        window.history.replaceState({}, '', url);
        
        // Reload page to show new tab content
        window.location.reload();
    }

    // Domain Status Toggle Functionality with disabled state handling
    setupDomainToggles() {
        const toggles = document.querySelectorAll('.domain-status-toggle');
        
        toggles.forEach(toggle => {
            // Check if domain is verified
            const isVerified = toggle.getAttribute('data-verified') === 'true';
            const verificationStatus = toggle.getAttribute('data-verification-status');

            if (!isVerified && verificationStatus === 'PENDING') {
                const label = toggle.closest('label');
                label.title = 'Domain must be verified before it can be activated';

                toggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.showToast('Domain must be verified before it can be activated', 'warning');
                });

                return;
            }
            
            // Set up change handler only for verified domains
            toggle.addEventListener('change', async (e) => {
                const domainId = e.target.getAttribute('data-domain-id');
                const isActive = e.target.checked;
                
                // Optimistic UI update
                this.setToggleLoading(e.target, true);
                
                try {
                    const success = await this.updateDomainStatus(domainId, isActive);
                    
                    if (success) {
                        this.showToast(`Domain ${isActive ? 'activated' : 'deactivated'} successfully`, 'success');
                    } else {
                        // Revert toggle on failure
                        e.target.checked = !isActive;
                        this.showToast('Failed to update domain status', 'error');
                    }
                } catch (error) {
                    console.error('Domain status update failed:', error);
                    // Revert toggle on error
                    e.target.checked = !isActive;
                    this.showToast('Network error: Could not update domain status', 'error');
                } finally {
                    this.setToggleLoading(e.target, false);
                }
            });
        });
    }

    // Update domain status via API
    async updateDomainStatus(domainId, isActive) {
        try {
            const response = await fetch(`/api/config/domains/${domainId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ active: isActive })
            });

            if (response.ok) {
                const data = await response.json();
                return data.success;
            }
            return false;
        } catch (error) {
            console.error('API error:', error);
            return false;
        }
    }

    // Visual loading state for toggles with enhanced animations
    setToggleLoading(toggle, isLoading) {
        const toggleWrapper = toggle.nextElementSibling; // The visual toggle div
        const tableRow = toggle.closest('tr');
        
        if (isLoading) {
            toggleWrapper.classList.add('toggle-loading');
            tableRow.classList.add('row-loading');
            toggle.disabled = true;
        } else {
            toggleWrapper.classList.remove('toggle-loading');
            tableRow.classList.remove('row-loading');
            toggle.disabled = false;
        }
    }

    // Table Sorting Functionality
    setupTableSorting() {
        const sortableHeaders = document.querySelectorAll('.sortable');
        let currentSort = { column: null, direction: 'asc' };

        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const sortBy = header.getAttribute('data-sort');
                
                // Toggle direction if same column, otherwise default to asc
                if (currentSort.column === sortBy) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.direction = 'asc';
                }
                currentSort.column = sortBy;

                // Update header visual state
                this.updateSortHeaders(header, currentSort.direction);
                
                // Sort the table
                this.sortTable(sortBy, currentSort.direction);
            });
        });
    }

    updateSortHeaders(activeHeader, direction) {
        // Remove sort classes from all headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
        });

        // Add appropriate class to active header
        activeHeader.classList.add(direction === 'asc' ? 'sort-asc' : 'sort-desc');
    }

    sortTable(column, direction) {
        const tbody = document.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        rows.sort((a, b) => {
            let aVal, bVal;

            switch (column) {
                case 'domain':
                    aVal = a.querySelector('td:nth-child(1) .text-gray-900').textContent.trim();
                    bVal = b.querySelector('td:nth-child(1) .text-gray-900').textContent.trim();
                    break;
                case 'status': {
                    // Sort by verification status: Verified > Warning > Unverified > Error
                    const statusOrder = { 'Verified': 4, 'Warning': 3, 'Unverified': 2, 'Error': 1 };
                    aVal = statusOrder[a.querySelector('td:nth-child(2) span').textContent.trim()] || 0;
                    bVal = statusOrder[b.querySelector('td:nth-child(2) span').textContent.trim()] || 0;
                    break;
                }
                case 'webhook':
                    aVal = a.querySelector('td:nth-child(3) span').textContent.trim();
                    bVal = b.querySelector('td:nth-child(3) span').textContent.trim();
                    break;
                case 'aliases':
                    aVal = parseInt(a.querySelector('td:nth-child(4)').textContent.trim()) || 0;
                    bVal = parseInt(b.querySelector('td:nth-child(4)').textContent.trim()) || 0;
                    break;
                default:
                    return 0;
            }

            // Compare values
            if (typeof aVal === 'string') {
                const result = aVal.localeCompare(bVal);
                return direction === 'asc' ? result : -result;
            } else {
                const result = aVal - bVal;
                return direction === 'asc' ? result : -result;
            }
        });

        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    }

    setupActionButtons() {
        // These functions are called from inline onclick handlers in the template
        // We'll make them available globally
        
        window.viewLogs = (domainId) => {
            console.log(`View logs for domain: ${domainId}`);
            this.showToast('Log viewer opening soon...', 'info');
            // TODO: Implement log viewer modal or redirect to logs tab with filter
        };

        window.deleteDomain = async (domainId, domainName) => {
            if (await this.confirmDelete(domainName)) {
                await this.performDomainDelete(domainId, domainName);
            }
        };

        window.showAliases = (domainId) => {
            console.log(`Show aliases for domain: ${domainId}`);
            this.showToast('Alias viewer opening soon...', 'info');
            // TODO: Implement alias viewer modal or redirect to aliases tab with filter
        };
    }

    // Enhanced confirmation dialog for delete
    async confirmDelete(domainName) {
        return new Promise((resolve) => {
            // Create modal backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fixed inset-0 z-50 flex items-center justify-center p-4';
            backdrop.style.background = 'rgba(0, 0, 0, 0.5)';
            backdrop.style.backdropFilter = 'blur(4px)';
            
            // Create confirmation dialog
            const dialog = document.createElement('div');
            dialog.className = 'confirmation-dialog max-w-md w-full p-6 text-center bg-white rounded-xl shadow-xl';
            dialog.style.transform = 'translateY(20px) scale(0.95)';
            dialog.style.transition = 'all 0.2s ease-out';
            
            dialog.innerHTML = `
                <div class="mb-4">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Delete Domain</h3>
                    <p class="text-sm text-gray-600 mb-4">
                        Are you sure you want to delete <strong>"${domainName}"</strong>?
                    </p>
                    <div class="text-xs text-gray-500 text-left bg-gray-50 p-3 rounded-lg mb-4">
                        <p class="font-medium mb-2">This will permanently delete:</p>
                        <ul class="space-y-1">
                            <li>• The domain configuration</li>
                            <li>• All associated aliases</li>
                            <li>• All email logs for this domain</li>
                        </ul>
                        <p class="font-medium mt-2 text-red-600">This action cannot be undone.</p>
                    </div>
                </div>
                <div class="flex space-x-3 justify-center">
                    <button type="button" class="cancel-btn px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium">
                        Cancel
                    </button>
                    <button type="button" class="confirm-btn px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium">
                        Delete domain
                    </button>
                </div>
            `;
            
            backdrop.appendChild(dialog);
            document.body.appendChild(backdrop);
            
            // Animate in
            requestAnimationFrame(() => {
                backdrop.style.opacity = '1';
                dialog.style.transform = 'translateY(0) scale(1)';
            });
            
            // Handle button clicks
            const cancelBtn = dialog.querySelector('.cancel-btn');
            const confirmBtn = dialog.querySelector('.confirm-btn');
            
            const cleanup = () => {
                backdrop.style.opacity = '0';
                dialog.style.transform = 'translateY(20px) scale(0.95)';
                setTimeout(() => {
                    if (backdrop.parentNode) {
                        backdrop.remove();
                    }
                }, 150);
            };
            
            cancelBtn.addEventListener('click', () => {
                cleanup();
                resolve(false);
            });
            
            confirmBtn.addEventListener('click', () => {
                cleanup();
                resolve(true);
            });
            
            // Close on backdrop click
            backdrop.addEventListener('click', (e) => {
                if (e.target === backdrop) {
                    cleanup();
                    resolve(false);
                }
            });
            
            // Close on escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    cleanup();
                    resolve(false);
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        });
    }

    // Perform domain deletion
    async performDomainDelete(domainId, domainName) {
        try {
            // Show loading state
            this.showToast('Deleting domain...', 'info');
            
            const response = await fetch(`/api/config/domains/${domainName}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showToast(`Domain "${domainName}" deleted successfully`, 'success');
                
                // Remove the row from the table with animation
                this.removeTableRow(domainId);
                
                // Reload page after a short delay to update counts
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                const errorData = await response.json();
                this.showToast(`Failed to delete domain: ${errorData.message || 'Unknown error'}`, 'error');
            }
        } catch (error) {
            console.error('Delete domain error:', error);
            this.showToast('Network error: Could not delete domain', 'error');
        }
    }

    // Remove table row with animation
    removeTableRow(domainId) {
        const toggleElement = document.querySelector(`[data-domain-id="${domainId}"]`);
        if (toggleElement) {
            const row = toggleElement.closest('tr');
            if (row) {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-20px)';
                
                setTimeout(() => {
                    row.remove();
                }, 300);
            }
        }
    }

    // Enhanced toast notification system
    showToast(message, type = 'info') {
        // Remove existing toast if any
        const existingToast = document.getElementById('toast-notification');
        if (existingToast) {
            existingToast.remove();
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.id = 'toast-notification';
        toast.className = `toast-notification fixed top-6 right-6 z-50 px-6 py-4 rounded-xl text-white font-medium shadow-lg transform transition-all duration-300 ease-out translate-y-[-100px] opacity-0 min-w-[300px] max-w-[400px]`;
        
        // Set background color based on type
        const typeStyles = {
            success: 'bg-green-600',
            error: 'bg-red-600', 
            warning: 'bg-orange-600',
            info: 'bg-blue-600'
        };
        toast.classList.add(typeStyles[type] || typeStyles.info);
        
        // Add icon based on type
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        
        const icon = document.createElement('span');
        icon.className = 'inline-block mr-3 text-lg';
        icon.textContent = icons[type] || icons.info;
        
        const messageSpan = document.createElement('span');
        messageSpan.textContent = message;
        
        toast.appendChild(icon);
        toast.appendChild(messageSpan);
        
        // Add to page
        document.body.appendChild(toast);
        
        // Animate in
        requestAnimationFrame(() => {
            toast.classList.remove('translate-y-[-100px]', 'opacity-0');
            toast.classList.add('translate-y-0', 'opacity-100');
        });
        
        // Auto remove after 4 seconds with enhanced exit animation
        setTimeout(() => {
            toast.classList.add('translate-x-[400px]', 'opacity-0');
            toast.classList.remove('translate-y-0', 'opacity-100');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 4000);
    }

    // Dropdown Management
    setupDropdowns() {
        // User dropdown
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenuDropdown = document.getElementById('user-menu-dropdown');
        
        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown(userMenuDropdown);
            });
        }

        // Create dropdown
        const createMenuButton = document.getElementById('create-menu-button');
        const createMenuDropdown = document.getElementById('create-menu-dropdown');
        
        if (createMenuButton && createMenuDropdown) {
            createMenuButton.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown(createMenuDropdown);
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', () => {
            this.closeAllDropdowns();
        });

        // Close dropdowns on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });
    }

    toggleDropdown(dropdown) {
        const isHidden = dropdown.classList.contains('hidden');
        
        // Close all dropdowns first
        this.closeAllDropdowns();
        
        if (isHidden) {
            this.openDropdown(dropdown);
        }
    }

    openDropdown(dropdown) {
        dropdown.classList.remove('hidden');
        dropdown.classList.remove('opacity-0', 'scale-95');
        dropdown.classList.add('opacity-100', 'scale-100');
    }

    closeDropdown(dropdown) {
        dropdown.classList.add('opacity-0', 'scale-95');
        dropdown.classList.remove('opacity-100', 'scale-100');
        setTimeout(() => {
            dropdown.classList.add('hidden');
        }, 150);
    }

    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('[id$="-dropdown"]');
        dropdowns.forEach(dropdown => {
            if (!dropdown.classList.contains('hidden')) {
                this.closeDropdown(dropdown);
            }
        });
    }

    // Update create button text based on active tab
    updateCreateButton() {
        const createButtonText = document.getElementById('create-button-text');
        if (createButtonText) {
            const tabMap = {
                'domains': 'Create domain',
                'aliases': 'Create alias', 
                'webhooks': 'Create webhook',
                'logs': 'View logs'
            };
            createButtonText.textContent = tabMap[this.activeTab] || 'Create domain';
        }
    }

    // Load metrics from API
    async loadMetrics() {
        try {
            const response = await fetch('/api/dashboard/metrics');
            if (response.ok) {
                const metrics = await response.json();
                const metricsContent = document.getElementById('metrics-content');
                if (metricsContent) {
                    metricsContent.textContent = `${metrics.emails_processed_24h} emails processed in last 24hr • ${metrics.success_rate}% success rate`;
                }
            }
        } catch (error) {
            console.error('Failed to load metrics:', error);
            const metricsContent = document.getElementById('metrics-content');
            if (metricsContent) {
                metricsContent.textContent = 'Unable to load metrics';
            }
        }
    }
}

// Global modal functions (for create dropdown actions)
window.openModal = function(modalId) {
    console.log(`Opening modal: ${modalId}`);
    // Modal functionality will be implemented in next phase
    if (window.dashboard) {
        window.dashboard.showToast(`${modalId.replace('-modal', '').replace('-', ' ')} coming soon!`, 'info');
    } else {
        alert(`${modalId} functionality coming soon!`);
    }
};

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new UserDashboard();
});

// Make UserDashboard available globally
window.UserDashboard = UserDashboard;