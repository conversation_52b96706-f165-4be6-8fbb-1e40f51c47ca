// DaisyUI Dropdown Handler
// Simple, conflict-free dropdown functionality for DaisyUI components

class DropdownHandler {
    constructor() {
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupDropdowns());
        } else {
            this.setupDropdowns();
        }
    }

    setupDropdowns() {        
        // Handle dropdown toggles
        document.addEventListener('click', (e) => {
            const dropdown = e.target.closest('.dropdown');
            
            if (dropdown) {
                const button = dropdown.querySelector('[role="button"]');
                
                if (e.target === button || button.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other dropdowns
                    document.querySelectorAll('.dropdown').forEach(d => {
                        if (d !== dropdown) {
                            d.classList.remove('dropdown-open');
                        }
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('dropdown-open');
                }
            } else {
                // Close all dropdowns when clicking outside
                document.querySelectorAll('.dropdown').forEach(d => {
                    d.classList.remove('dropdown-open');
                });
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.querySelectorAll('.dropdown').forEach(d => {
                    d.classList.remove('dropdown-open');
                });
            }
        });
    }
}

// Initialize dropdown handler
const dropdownHandler = new DropdownHandler();

export default dropdownHandler;
