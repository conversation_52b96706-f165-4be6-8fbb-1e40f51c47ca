// Form Validation Module
class FormValidation {
    constructor() {
        this.validators = {
            required: this.validateRequired.bind(this),
            email: this.validateEmail.bind(this),
            url: this.validateUrl.bind(this),
            domain: this.validateDomain.bind(this),
            minLength: this.validateMinLength.bind(this),
            maxLength: this.validateMaxLength.bind(this),
            pattern: this.validatePattern.bind(this)
        };
    }

    /**
     * Validate a form based on rules
     * @param {Object} formData - Form data to validate
     * @param {Object} rules - Validation rules
     * @returns {Object} - Validation errors (empty if valid)
     */
    validateForm(formData, rules) {
        const errors = {};

        Object.keys(rules).forEach(field => {
            const value = formData[field];
            const fieldRules = rules[field];
            const fieldErrors = [];

            fieldRules.forEach(rule => {
                const validator = this.validators[rule.type];
                if (validator) {
                    const isValid = validator(value, rule.value);
                    if (!isValid) {
                        fieldErrors.push(rule.message || this.getDefaultMessage(rule.type, field, rule.value));
                    }
                }
            });

            if (fieldErrors.length > 0) {
                errors[field] = fieldErrors;
            }
        });

        return errors;
    }

    /**
     * Validate required fields
     */
    validateRequired(value) {
        return value !== null && value !== undefined && value.toString().trim().length > 0;
    }

    /**
     * Validate email format
     */
    validateEmail(email) {
        if (!email) return true; // Allow empty if not required
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate URL format
     */
    validateUrl(url) {
        if (!url) return true; // Allow empty if not required
        try {
            const urlObj = new URL(url);
            return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
        } catch {
            return false;
        }
    }

    /**
     * Validate domain format
     */
    validateDomain(domain) {
        if (!domain) return true; // Allow empty if not required
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
        return domainRegex.test(domain) && domain.length <= 253;
    }

    /**
     * Validate minimum length
     */
    validateMinLength(value, minLength) {
        if (!value) return true; // Allow empty if not required
        return value.toString().length >= minLength;
    }

    /**
     * Validate maximum length
     */
    validateMaxLength(value, maxLength) {
        if (!value) return true; // Allow empty if not required
        return value.toString().length <= maxLength;
    }

    /**
     * Validate against a pattern
     */
    validatePattern(value, pattern) {
        if (!value) return true; // Allow empty if not required
        const regex = new RegExp(pattern);
        return regex.test(value);
    }

    /**
     * Get default error message for validation type
     */
    getDefaultMessage(type, field, value) {
        const messages = {
            required: `${field} is required`,
            email: `${field} must be a valid email address`,
            url: `${field} must be a valid URL`,
            domain: `${field} must be a valid domain name`,
            minLength: `${field} must be at least ${value} characters`,
            maxLength: `${field} must be no more than ${value} characters`,
            pattern: `${field} format is invalid`
        };
        return messages[type] || `${field} is invalid`;
    }

    /**
     * Display validation errors in the UI
     */
    displayErrors(errors, formElement) {
        // Clear existing errors
        this.clearErrors(formElement);

        Object.keys(errors).forEach(field => {
            const fieldElement = formElement.querySelector(`[name="${field}"]`);
            if (fieldElement) {
                this.showFieldError(fieldElement, errors[field]);
            }
        });
    }

    /**
     * Show error for a specific field
     */
    showFieldError(fieldElement, errorMessages) {
        // Add error class to field
        fieldElement.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
        fieldElement.classList.remove('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error mt-1 text-sm text-red-600';
        errorElement.textContent = errorMessages[0]; // Show first error

        // Insert error message after the field
        fieldElement.parentNode.insertBefore(errorElement, fieldElement.nextSibling);
    }

    /**
     * Clear all validation errors from a form
     */
    clearErrors(formElement) {
        // Remove error classes from fields
        const fields = formElement.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
            field.classList.add('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');
        });

        // Remove error message elements
        const errorElements = formElement.querySelectorAll('.field-error');
        errorElements.forEach(element => element.remove());
    }

    /**
     * Validate a single field in real-time
     */
    validateField(fieldElement, rules) {
        const value = fieldElement.value;
        const fieldName = fieldElement.name;
        const fieldRules = rules[fieldName];

        if (!fieldRules) return true;

        const errors = this.validateForm({ [fieldName]: value }, { [fieldName]: fieldRules });
        
        // Clear existing errors for this field
        const existingError = fieldElement.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        if (errors[fieldName]) {
            this.showFieldError(fieldElement, errors[fieldName]);
            return false;
        } else {
            // Remove error styling
            fieldElement.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500');
            fieldElement.classList.add('border-gray-300', 'focus:border-blue-500', 'focus:ring-blue-500');
            return true;
        }
    }

    /**
     * Set up real-time validation for a form
     */
    setupRealTimeValidation(formElement, rules) {
        Object.keys(rules).forEach(fieldName => {
            const fieldElement = formElement.querySelector(`[name="${fieldName}"]`);
            if (fieldElement) {
                fieldElement.addEventListener('blur', () => {
                    this.validateField(fieldElement, rules);
                });
            }
        });
    }
}

// Export as singleton
window.formValidation = new FormValidation();
export default window.formValidation;
