// Tab Management Module
class TabManager {
    constructor() {
        this.activeTab = this.getActiveTabFromURL();
        this.init();
    }

    init() {
        this.setupTabs();
        this.updateCreateButton();
    }

    // Get active tab from URL parameter
    getActiveTabFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('tab') || 'domains';
    }

    // Tab Management
    setupTabs() {
        const tabLinks = document.querySelectorAll('.tab-link');
        
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = link.getAttribute('data-tab');
                this.switchToTab(tab);
            });
        });

        // Set initial active state
        this.setActiveTabVisual(this.activeTab);
    }

    switchToTab(tab) {
        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('tab', tab);
        window.history.replaceState({}, '', url);
        
        // Update active tab
        this.activeTab = tab;
        this.setActiveTabVisual(tab);
        this.updateCreateButton();
        
        // Reload page to show new tab content
        // In a SPA, you would update content dynamically instead
        window.location.reload();
    }

    setActiveTabVisual(activeTab) {
        const tabLinks = document.querySelectorAll('.tab-link');
        
        tabLinks.forEach(link => {
            const tab = link.getAttribute('data-tab');
            const isActive = tab === activeTab;
            
            // Update classes for active state
            if (isActive) {
                link.classList.add('border-blue-500', 'text-blue-600');
                link.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            } else {
                link.classList.remove('border-blue-500', 'text-blue-600');
                link.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            }
        });
    }

    // Update create button text based on active tab
    updateCreateButton() {
        const createButtonText = document.getElementById('create-button-text');
        if (createButtonText) {
            const tabMap = {
                'domains': 'Create domain',
                'aliases': 'Create alias', 
                'webhooks': 'Create webhook',
                'logs': 'View logs'
            };
            createButtonText.textContent = tabMap[this.activeTab] || 'Create domain';
        }

        // Update create button action
        const createButton = document.getElementById('create-button');
        if (createButton) {
            // Remove existing click handlers
            const newButton = createButton.cloneNode(true);
            createButton.parentNode.replaceChild(newButton, createButton);
            
            // Add new click handler based on active tab
            newButton.addEventListener('click', () => {
                this.handleCreateButtonClick();
            });
        }
    }

    handleCreateButtonClick() {
        const modalTypeMap = {
            'domains': 'create-domain',
            'aliases': 'create-alias',
            'webhooks': 'create-webhook',
            'logs': null // No create action for logs
        };

        const modalType = modalTypeMap[this.activeTab];
        if (modalType && window.openModal) {
            window.openModal(modalType);
        } else if (this.activeTab === 'logs') {
            // Handle logs tab - maybe open filters or refresh
            window.toast?.info('Log filters and refresh coming soon...');
        }
    }

    /**
     * Get current active tab
     */
    getActiveTab() {
        return this.activeTab;
    }

    /**
     * Check if a specific tab is active
     */
    isTabActive(tab) {
        return this.activeTab === tab;
    }

    /**
     * Get tab configuration
     */
    getTabConfig() {
        return {
            domains: {
                name: 'Domains',
                createText: 'Create domain',
                createModal: 'create-domain'
            },
            aliases: {
                name: 'Aliases',
                createText: 'Create alias',
                createModal: 'create-alias'
            },
            webhooks: {
                name: 'Webhooks',
                createText: 'Create webhook',
                createModal: 'create-webhook'
            },
            logs: {
                name: 'Logs',
                createText: 'View logs',
                createModal: null
            }
        };
    }
}

export default TabManager;
