// Metrics Loading Module
class MetricsLoader {
    constructor(apiClient) {
        this.apiClient = apiClient;
        this.metricsContainer = document.getElementById('metrics-content');
        this.refreshInterval = null;
        this.refreshRate = 30000; // 30 seconds
    }

    /**
     * Initialize metrics loading
     */
    async init() {
        await this.loadMetrics();
        this.startAutoRefresh();
    }

    /**
     * Load metrics from API
     */
    async loadMetrics() {
        if (!this.metricsContainer) return;

        try {
            // Show loading state
            this.showLoadingState();
            
            const metrics = await this.apiClient.getMetrics();
            this.displayMetrics(metrics);
            
        } catch (error) {
            console.error('Failed to load metrics:', error);
            this.showErrorState();
        }
    }

    /**
     * Display metrics in the UI
     */
    displayMetrics(metrics) {
        if (!this.metricsContainer) return;

        // Format the metrics display
        const emailsProcessed = metrics.emails_processed_24h || 0;
        const successRate = metrics.success_rate || 0;
        const totalDomains = metrics.total_domains || 0;
        const activeDomains = metrics.active_domains || 0;
        const totalAliases = metrics.total_aliases || 0;
        const totalWebhooks = metrics.total_webhooks || 0;

        // Create a more detailed metrics display
        this.metricsContainer.innerHTML = `
            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                <div class="flex items-center gap-2">
                    <span class="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
                    <span><strong>${emailsProcessed}</strong> emails processed (24h)</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                    <span><strong>${successRate}%</strong> success rate</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="inline-block w-2 h-2 bg-purple-500 rounded-full"></span>
                    <span><strong>${activeDomains}/${totalDomains}</strong> domains active</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="inline-block w-2 h-2 bg-orange-500 rounded-full"></span>
                    <span><strong>${totalAliases}</strong> aliases</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="inline-block w-2 h-2 bg-indigo-500 rounded-full"></span>
                    <span><strong>${totalWebhooks}</strong> webhooks</span>
                </div>
                <div class="ml-auto text-xs text-gray-400">
                    Last updated: ${new Date().toLocaleTimeString()}
                </div>
            </div>
        `;

        // Add success class for visual feedback
        this.metricsContainer.classList.remove('loading', 'error');
        this.metricsContainer.classList.add('loaded');
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        if (!this.metricsContainer) return;

        this.metricsContainer.innerHTML = `
            <div class="flex items-center gap-2 text-sm text-gray-500">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                <span>Loading metrics...</span>
            </div>
        `;
        
        this.metricsContainer.classList.add('loading');
        this.metricsContainer.classList.remove('loaded', 'error');
    }

    /**
     * Show error state
     */
    showErrorState() {
        if (!this.metricsContainer) return;

        this.metricsContainer.innerHTML = `
            <div class="flex items-center gap-2 text-sm text-red-600">
                <span>⚠</span>
                <span>Unable to load metrics</span>
                <button class="ml-2 text-blue-600 hover:text-blue-800 underline" onclick="window.metricsLoader?.loadMetrics()">
                    Retry
                </button>
            </div>
        `;
        
        this.metricsContainer.classList.add('error');
        this.metricsContainer.classList.remove('loaded', 'loading');
    }

    /**
     * Start auto-refresh of metrics
     */
    startAutoRefresh() {
        // Clear existing interval
        this.stopAutoRefresh();
        
        // Set new interval
        this.refreshInterval = setInterval(() => {
            this.loadMetrics();
        }, this.refreshRate);
    }

    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Manually refresh metrics
     */
    async refresh() {
        await this.loadMetrics();
    }

    /**
     * Set refresh rate
     */
    setRefreshRate(milliseconds) {
        this.refreshRate = milliseconds;
        if (this.refreshInterval) {
            this.startAutoRefresh(); // Restart with new rate
        }
    }

    /**
     * Destroy the metrics loader
     */
    destroy() {
        this.stopAutoRefresh();
    }
}

export default MetricsLoader;
