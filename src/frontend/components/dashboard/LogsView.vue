<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import type { Domain, Alias } from '../../types'

// State
const domains = ref<Domain[]>([])
const aliases = ref<Alias[]>([])
const logs = ref<any[]>([])
const selectedDomain = ref('')
const selectedAlias = ref('')
const isLoading = ref(true)
const isLoadingLogs = ref(false)

// Computed
const filteredAliases = computed(() => {
  if (!selectedDomain.value) return []
  return aliases.value.filter(alias => alias.domainId === selectedDomain.value)
})

// Load initial data
const loadDomains = async () => {
  try {
    const response = await fetch('/api/domains')
    const data = await response.json()
    domains.value = data.domains || []
  } catch (error) {
    console.error('Failed to load domains:', error)
  }
}

const loadAliases = async () => {
  try {
    const response = await fetch('/api/aliases')
    const data = await response.json()
    aliases.value = data.aliases || []
  } catch (error) {
    console.error('Failed to load aliases:', error)
  }
}

const loadLogs = async () => {
  if (!selectedDomain.value) {
    logs.value = []
    return
  }

  try {
    isLoadingLogs.value = true
    const params = new URLSearchParams({
      domainId: selectedDomain.value
    })
    
    if (selectedAlias.value) {
      params.append('aliasId', selectedAlias.value)
    }

    const response = await fetch(`/api/logs?${params}`)
    const data = await response.json()
    logs.value = data.logs || []
  } catch (error) {
    console.error('Failed to load logs:', error)
    logs.value = []
  } finally {
    isLoadingLogs.value = false
  }
}

// Watch for changes in selections
watch(selectedDomain, () => {
  selectedAlias.value = '' // Reset alias when domain changes
  loadLogs()
})

watch(selectedAlias, () => {
  loadLogs()
})

onMounted(async () => {
  isLoading.value = true
  await Promise.all([loadDomains(), loadAliases()])

  // Check for pre-selected domain/alias from navigation
  const preSelectedDomain = sessionStorage.getItem('logsView_selectedDomain')
  const preSelectedAlias = sessionStorage.getItem('logsView_selectedAlias')

  if (preSelectedDomain) {
    selectedDomain.value = preSelectedDomain
    sessionStorage.removeItem('logsView_selectedDomain')

    if (preSelectedAlias) {
      selectedAlias.value = preSelectedAlias
      sessionStorage.removeItem('logsView_selectedAlias')
    }

    // Load logs for the pre-selected domain/alias
    await loadLogs()
  }

  isLoading.value = false
})

// Quick access method for external calls
const viewLogsFor = (domainId: string, aliasId?: string) => {
  selectedDomain.value = domainId
  if (aliasId) {
    selectedAlias.value = aliasId
  }
}

// View log details (placeholder for future modal implementation)
const viewLogDetails = (log: any) => {
  console.log('View log details:', log)
  // TODO: Implement log details modal
  alert(`Log Details:\n\nFrom: ${log.fromAddress}\nTo: ${log.toAddresses.join(', ')}\nSubject: ${log.subject || '(no subject)'}\nStatus: ${log.deliveryStatus}\nTimestamp: ${new Date(log.createdAt).toLocaleString()}`)
}

// Expose methods for parent components
defineExpose({
  viewLogsFor,
  refresh: () => Promise.all([loadDomains(), loadAliases(), loadLogs()])
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <div v-else class="space-y-6">
      <!-- Selectors -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Domain selector -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">Select Domain</span>
          </label>
          <select v-model="selectedDomain" class="select select-bordered">
            <option value="">Choose a domain...</option>
            <option v-for="domain in domains" :key="domain.id" :value="domain.id">
              {{ domain.domainName || domain.domain }}
            </option>
          </select>
        </div>

        <!-- Alias selector -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">Select Alias (optional)</span>
          </label>
          <select 
            v-model="selectedAlias" 
            class="select select-bordered"
            :disabled="!selectedDomain || filteredAliases.length === 0"
          >
            <option value="">All aliases</option>
            <option v-for="alias in filteredAliases" :key="alias.id" :value="alias.id">
              {{ alias.email.includes('*@') ? 'catch-all' : alias.email.split('@')[0] }}
            </option>
          </select>
        </div>
      </div>

      <!-- Logs viewer -->
      <div class="card bg-base-100 border border-base-300">
        <div class="card-header px-6 py-4 border-b border-base-300">
          <h3 class="text-lg font-medium">Email Logs</h3>
        </div>
        <div class="card-body p-0">
          <!-- Loading logs -->
          <div v-if="isLoadingLogs" class="flex justify-center py-8">
            <span class="loading loading-spinner loading-md"></span>
          </div>

          <!-- No domain selected -->
          <div v-else-if="!selectedDomain" class="p-8 text-center text-base-content/60">
            <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p>Select a domain to view email logs</p>
          </div>

          <!-- No logs found -->
          <div v-else-if="logs.length === 0" class="p-8 text-center text-base-content/60">
            <svg class="w-12 h-12 mx-auto mb-4 text-base-content/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-4.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
            <p>No email logs found for the selected criteria</p>
          </div>

          <!-- Logs table -->
          <div v-else class="overflow-x-auto">
            <table class="table table-zebra">
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>From</th>
                  <th>To</th>
                  <th>Subject</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="log in logs" :key="log.id">
                  <td class="text-sm">
                    {{ new Date(log.createdAt).toLocaleString() }}
                  </td>
                  <td class="text-sm">
                    <div class="truncate max-w-[200px]" :title="log.fromAddress">
                      {{ log.fromAddress }}
                    </div>
                  </td>
                  <td class="text-sm">
                    <div class="truncate max-w-[200px]" :title="log.toAddresses.join(', ')">
                      {{ log.toAddresses.join(', ') }}
                    </div>
                  </td>
                  <td class="text-sm">
                    <div class="truncate max-w-[300px]" :title="log.subject">
                      {{ log.subject || '(no subject)' }}
                    </div>
                  </td>
                  <td>
                    <div class="badge" :class="{
                      'badge-success': log.deliveryStatus === 'DELIVERED',
                      'badge-error': log.deliveryStatus === 'FAILED',
                      'badge-warning': log.deliveryStatus === 'PENDING' || log.deliveryStatus === 'RETRYING',
                      'badge-neutral': log.deliveryStatus === 'EXPIRED'
                    }">
                      {{ log.deliveryStatus }}
                    </div>
                  </td>
                  <td>
                    <button class="btn btn-ghost btn-xs" @click="viewLogDetails(log)">
                      View
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
