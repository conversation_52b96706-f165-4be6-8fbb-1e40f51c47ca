<script setup lang="ts">
import { ref, onMounted } from 'vue'
import TabNavigation from './TabNavigation.vue'

// Props from server-side data
interface DashboardData {
  activeTab: string
  counts: {
    domains: number
    aliases: number
    webhooks: number
  }
}

const dashboardData = ref<DashboardData>({
  activeTab: (window as any).dashboardData?.activeTab || 'domains',
  counts: (window as any).dashboardData?.counts || { domains: 0, aliases: 0, webhooks: 0 }
})

// Methods
const handleCreateAction = (actionType: string) => {
  // Use the existing global modal system
  if ((window as any).openModal) {
    (window as any).openModal(actionType)
  } else {
    console.warn('Modal system not available')
  }
}

onMounted(() => {
  // Tab navigation ready
})
</script>

<template>
  <TabNavigation 
    :active-tab="dashboardData.activeTab"
    :counts="dashboardData.counts"
    @create-action="handleCreateAction"
  />
</template>
