import { createRouter, createWebHistory } from 'vue-router'
import DomainsView from './components/dashboard/DomainsView.vue'
import AliasesView from './components/dashboard/AliasesView.vue'
import WebhooksView from './components/dashboard/WebhooksView.vue'
import LogsView from './components/dashboard/LogsView.vue'
import SettingsPage from './components/settings/SettingsPage.vue'

const routes = [
  {
    path: '/',
    redirect: '/domains'
  },
  {
    path: '/domains',
    name: 'domains',
    component: DomainsView
  },
  {
    path: '/aliases',
    name: 'aliases',
    component: AliasesView
  },
  {
    path: '/webhooks',
    name: 'webhooks',
    component: WebhooksView
  },
  {
    path: '/logs',
    name: 'logs',
    component: LogsView
  },
  {
    path: '/settings',
    name: 'settings',
    component: SettingsPage
  },
] as const

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
