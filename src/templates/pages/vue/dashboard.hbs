<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - User Dashboard</title>
    <meta name="description" content="Manage your domains, webhooks, and email aliases">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    {{#if dashboardData}}
    <script>
      window.dashboardData = {{{dashboardData}}};
    </script>
    {{/if}}

    {{#if isDevelopment}}
    <script type="module" src="http://localhost:5173/src/frontend/dashboard-vue.ts"></script>
    {{else}}
    <script type="module" src="/assets/dashboard-vue.js"></script>
    {{/if}}

    <!-- DaisyUI Dropdown Handler -->
    <script type="module" src="/public/js/modules/ui/dropdown-handler.js"></script>
    <script type="module" src="/public/js/user.js"></script>
</head>
<body class="min-h-screen bg-white" data-theme="eu-theme">
    <div id="dashboard-vue-app"></div>
</body>
</html>
