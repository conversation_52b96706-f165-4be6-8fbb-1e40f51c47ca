{{#> layouts/guest}}
<div class="flex items-center justify-center min-h-screen px-4 py-12 bg-gray-50 sm:px-6 lg:px-8">
  <div class="w-full max-w-md space-y-8">
    <div>
      <h2 class="mt-6 text-3xl font-extrabold text-center text-gray-900">
        Sign in to your account
      </h2>
      <p class="mt-2 text-sm text-center text-gray-600">
        Or
        <a href="/register" class="font-medium text-blue-600 hover:text-blue-500">
          create a new account
        </a>
      </p>
    </div>
    <form class="mt-8 space-y-6" action="/login" method="POST">
      <div class="-space-y-px rounded-md shadow-sm">
        <div>
          <label for="email" class="sr-only">Email address</label>
          <input id="email" name="email" type="email" autocomplete="email" required 
                 class="relative block w-full px-3 py-2 text-gray-900 placeholder-gray-500 border border-gray-300 rounded-none appearance-none rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                 placeholder="Email address">
        </div>
        <div>
          <label for="password" class="sr-only">Password</label>
          <input id="password" name="password" type="password" autocomplete="current-password" required 
                 class="relative block w-full px-3 py-2 text-gray-900 placeholder-gray-500 border border-gray-300 rounded-none appearance-none rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                 placeholder="Password">
        </div>
      </div>

      <div>
        <button type="submit" 
                class="relative flex justify-center w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md group hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Sign in
        </button>
      </div>
    </form>

    {{#if error}}
    <div class="p-4 rounded-md bg-red-50">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            {{error}}
          </h3>
        </div>
      </div>
    </div>
    {{/if}}
  </div>
</div>

<script>
// Handle form submission with AJAX
document.querySelector('form').addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const data = Object.fromEntries(formData);
  
  try {
    const response = await fetch('/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    
    const result = await response.json();
    
    if (response.ok) {
      // Success - redirect to dashboard
      window.location.href = '/dashboard';
    } else {
      // Show error
      const errorDiv = document.querySelector('.bg-red-50');
      if (errorDiv) {
        errorDiv.querySelector('h3').textContent = result.error;
        errorDiv.style.display = 'block';
      } else {
        // Create error div if it doesn't exist
        const errorHtml = `
          <div class="p-4 mt-4 rounded-md bg-red-50">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  ${result.error}
                </h3>
              </div>
            </div>
          </div>
        `;
        e.target.insertAdjacentHTML('afterend', errorHtml);
      }
    }
  } catch (error) {
    console.error('Login error:', error);
    alert('An error occurred. Please try again.');
  }
});
</script>
{{/layouts/guest}}
