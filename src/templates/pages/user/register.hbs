{{#> layouts/guest}}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Create your account
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Or
        <a href="/login" class="font-medium text-blue-600 hover:text-blue-500">
          sign in to your existing account
        </a>
      </p>
    </div>
    <form class="mt-8 space-y-6" action="/register" method="POST">
      <div class="rounded-md shadow-sm space-y-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Name (optional)</label>
          <input id="name" name="name" type="text" 
                 class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                 placeholder="Your name">
        </div>
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
          <input id="email" name="email" type="email" autocomplete="email" required 
                 class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                 placeholder="Email address">
        </div>
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <input id="password" name="password" type="password" autocomplete="new-password" required 
                 class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                 placeholder="Password">
          <p class="mt-1 text-sm text-gray-500">
            Must be at least 8 characters with uppercase, lowercase, and number
          </p>
        </div>
      </div>

      <div class="bg-blue-50 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Free Plan includes:</h3>
            <ul class="mt-2 text-sm text-blue-700 list-disc list-inside">
              <li>50 emails per month</li>
              <li>Unlimited domains and webhooks</li>
              <li>Real-time delivery</li>
            </ul>
          </div>
        </div>
      </div>

      <div>
        <button type="submit" 
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Create Account
        </button>
      </div>
    </form>
  </div>
</div>


<script>
// Simple form submission - let server handle redirect
// Remove AJAX to avoid cookie timing issues

function showError(message) {
  // Remove existing error
  const existingError = document.querySelector('.bg-red-50');
  if (existingError) {
    existingError.remove();
  }
  
  // Add new error
  const errorHtml = `
    <div class="rounded-md bg-red-50 p-4 mt-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            ${message}
          </h3>
        </div>
      </div>
    </div>
  `;
  document.querySelector('form').insertAdjacentHTML('afterend', errorHtml);
}
</script>
{{/layouts/guest}}
