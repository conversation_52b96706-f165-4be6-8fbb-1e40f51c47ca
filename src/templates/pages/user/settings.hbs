<div class="px-6 py-8 mx-auto max-w-7xl lg:px-8">
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
        <p class="mt-2 text-gray-600">Manage your account settings and preferences.</p>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Settings Navigation -->
        <div class="lg:col-span-1">
            <nav class="space-y-1">
                <a href="#profile" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 border-r-2 border-blue-600 bg-blue-50 rounded-l-md">
                    <svg class="w-5 h-5 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    Profile
                </a>
                {{!-- <a href="#security" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-l-md">
                    <svg class="w-5 h-5 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Security
                </a>
                <a href="#notifications" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-l-md">
                    <svg class="w-5 h-5 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z" />
                    </svg>
                    Notifications
                </a> --}}
            </nav>
        </div>

        <!-- Settings Content -->
        <div class="lg:col-span-2">
            <!-- Coming Soon Notice -->
            <div class="p-4 mb-6 border border-yellow-200 rounded-lg bg-yellow-50">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Settings coming soon</h3>
                        <p class="mt-1 text-sm text-yellow-700">
                            This settings page is currently a placeholder. Full functionality is due in a future update.
                        </p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow">
                <!-- Profile Section -->
                <div id="profile" class="p-6">
                    <h2 class="mb-4 text-lg font-medium text-gray-900">Profile Information</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-700">Email Address</label>
                            <input type="email" 
                                    value="{{user.email}}" 
                                    disabled
                                    class="w-full px-3 py-2 text-gray-500 border border-gray-300 rounded-lg cursor-not-allowed bg-gray-50">
                            <p class="mt-1 text-xs text-gray-500">Email address cannot be changed at this time.</p>
                        </div>

                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-700">Display Name</label>
                            <input type="text" 
                                    value="{{user.name}}" 
                                    placeholder="Enter your display name"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div class="pt-4">
                            <button type="button" 
                                    class="px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700">
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>

                <hr class="border-gray-200">

                <!-- Security Section -->
                {{!-- <div id="security" class="p-6">
                    <h2 class="mb-4 text-lg font-medium text-gray-900">Security</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <h3 class="mb-2 text-sm font-medium text-gray-900">Change Password</h3>
                            <div class="space-y-3">
                                <input type="password" 
                                        placeholder="Current password"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <input type="password" 
                                        placeholder="New password"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <input type="password" 
                                        placeholder="Confirm new password"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div class="pt-3">
                                <button type="button" 
                                        class="px-4 py-2 text-white transition-colors bg-red-600 rounded-lg hover:bg-red-700">
                                    Update Password
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="border-gray-200">

                <!-- Notifications Section -->
                <div id="notifications" class="p-6">
                    <h2 class="mb-4 text-lg font-medium text-gray-900">Notification Preferences</h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">Email Notifications</h3>
                                <p class="text-sm text-gray-500">Receive notifications about webhook deliveries</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">Failure Alerts</h3>
                                <p class="text-sm text-gray-500">Get notified when webhook deliveries fail</p>
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer" checked>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>

                        <div class="pt-4">
                            <button type="button" 
                                    class="px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700">
                                Save Preferences
                            </button>
                        </div>
                    </div>
                </div> --}}
            </div>
        </div>
    </div>
</div>