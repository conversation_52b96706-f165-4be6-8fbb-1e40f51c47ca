<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - User Dashboard</title>

    {{#if dashboardData}}
    <script>
      window.dashboardData = {{{dashboardData}}};
    </script>
    {{/if}}
    {{#if isDevelopment}}
    <!-- Development mode: Load CSS and JS from Vite dev server -->
    <link rel="stylesheet" href="http://localhost:5173/src/frontend/style.css">
    <script type="module" src="http://localhost:5173/src/frontend/dashboard.ts"></script>
    {{else}}
    <!-- Production mode: Load built assets -->
    {{#each (viteAssetCss 'dashboard')}}
    <link rel="stylesheet" href="{{this}}">
    {{/each}}
    {{#each (viteAssetJs 'dashboard')}}
    <script type="module" src="{{this}}"></script>
    {{/each}}
    {{/if}}

    <!-- Fallback: Ensure basic Tailwind/DaisyUI is always available -->
    <script>
      // Check if CSS is loaded, if not, add fallback
      setTimeout(() => {
        const testEl = document.createElement('div');
        testEl.className = 'bg-primary';
        document.body.appendChild(testEl);
        const styles = window.getComputedStyle(testEl);
        if (!styles.backgroundColor || styles.backgroundColor === 'rgba(0, 0, 0, 0)') {
          console.warn('CSS not loaded, adding fallback');
          const fallbackCSS = document.createElement('link');
          fallbackCSS.rel = 'stylesheet';
          fallbackCSS.href = 'https://cdn.jsdelivr.net/npm/daisyui@5.0.43/dist/full.css';
          document.head.appendChild(fallbackCSS);
        }
        document.body.removeChild(testEl);
      }, 100);
    </script>
</head>
<body class="min-h-screen bg-white" data-theme="eu-theme">
    <!-- Vue User Header -->
    <div id="vue-user-header"></div>

    <!-- Vue Tab Navigation Container -->
    <div id="vue-tabs"></div>

    <!-- Alpine.js tabs removed - Vue tabs are now primary -->

    <main class="px-6 mx-auto max-w-7xl lg:px-8">
        {{{body}}}
    </main>

    <!-- Vue 3 Dashboard App (for modals and components) -->
    <div id="dashboard-app" data-active-tab="{{activeTab}}" data-dashboard-data="{{dashboardData}}"></div>

    <!-- Vue Modal System is now handled in DashboardApp.vue -->

    <!-- DaisyUI Dropdown Handler -->
    <script type="module" src="/public/js/modules/ui/dropdown-handler.js"></script>

    <script type="module" src="/public/js/user.js"></script>
</body>
</html>