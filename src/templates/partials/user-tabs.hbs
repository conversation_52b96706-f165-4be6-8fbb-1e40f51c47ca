<!-- Tab Navigation -->
<div class="bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="flex justify-between items-center">
            <!-- Tab Navigation -->
            <nav class="flex space-x-8" aria-label="Tabs">
                <a href="/dashboard?tab=domains" 
                   class="tab-link {{#if (eq activeTab 'domains')}}border-blue-600 text-blue-600{{else}}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{{/if}} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                   data-tab="domains">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                        </svg>
                        <span>Domains</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{#if counts.domains}}{{counts.domains}}{{else}}0{{/if}}
                        </span>
                    </div>
                </a>
                
                <a href="/dashboard?tab=aliases" 
                   class="tab-link {{#if (eq activeTab 'aliases')}}border-blue-600 text-blue-600{{else}}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{{/if}} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                   data-tab="aliases">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                        </svg>
                        <span>Aliases</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{#if counts.aliases}}{{counts.aliases}}{{else}}0{{/if}}
                        </span>
                    </div>
                </a>

                <a href="/dashboard?tab=webhooks" 
                   class="tab-link {{#if (eq activeTab 'webhooks')}}border-blue-600 text-blue-600{{else}}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{{/if}} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                   data-tab="webhooks">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        <span>Webhooks</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{#if counts.webhooks}}{{counts.webhooks}}{{else}}0{{/if}}
                        </span>
                    </div>
                </a>

                <a href="/dashboard?tab=logs" 
                   class="tab-link {{#if (eq activeTab 'logs')}}border-blue-600 text-blue-600{{else}}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{{/if}} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors"
                   data-tab="logs">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span>Logs</span>
                    </div>
                </a>
            </nav>

            <!-- Split Button: Main Action + Dropdown -->
            <div class="relative" x-data="{ open: false }" @click.away="open = false">
                <div class="flex">
                    <!-- Main Create Button (Dynamic) -->
                    <button type="button"
                            id="create-button"
                            class="inline-flex items-center px-3 sm:px-4 py-2 border border-transparent text-sm font-medium rounded-l-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
>
                        <svg class="-ml-1 mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        <span class="hidden sm:inline" id="create-button-text">
                            {{#if (eq activeTab 'domains')}}Create domain{{/if}}
                            {{#if (eq activeTab 'aliases')}}Create alias{{/if}}
                            {{#if (eq activeTab 'webhooks')}}Create webhook{{/if}}
                            {{#if (eq activeTab 'logs')}}View logs{{/if}}
                        </span>
                        <span class="sm:hidden">Create</span>
                    </button>

                    <!-- Dropdown Toggle Button -->
                    <button type="button"
                            class="inline-flex items-center px-2 py-2 border-l border-blue-500 text-sm font-medium rounded-r-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                            @click="open = !open"
                            :aria-expanded="open"
                            aria-haspopup="true">
                        <svg class="h-4 w-4 sm:h-5 sm:w-5 transition-transform duration-150"
                             :class="{ 'rotate-180': open }"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                </div>

                <!-- Dropdown menu -->
                <div class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 transition-all duration-150 ease-in-out z-10"
                     x-show="open"
                     x-transition:enter="transition ease-out duration-150"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     role="menu"
                     aria-orientation="vertical"
                     @keydown.escape="open = false">
                    <div class="py-1" role="none">
                        <button type="button"
                                class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                role="menuitem"
                                onclick="openModal('create-domain')"
                                @click="open = false">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                                </svg>
                                <span>Create domain</span>
                            </div>
                        </button>
                        <button type="button"
                                class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                role="menuitem"
                                onclick="openModal('create-alias')"
                                @click="open = false">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                </svg>
                                <span>Create alias</span>
                            </div>
                        </button>
                        <button type="button"
                                class="w-full text-left block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                role="menuitem"
                                onclick="openModal('create-webhook')"
                                @click="open = false">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                                </svg>
                                <span>Create webhook</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>