<!-- Metrics Pill -->
<div class="bg-white py-6">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
        <div class="flex justify-center">
            <div class="inline-flex items-center px-4 sm:px-6 py-3 bg-gray-100 rounded-full text-sm text-gray-700 font-medium max-w-full">
                <div id="metrics-content" class="flex flex-wrap items-center justify-center gap-2 sm:gap-4 text-xs sm:text-sm">
                    <div class="flex items-center gap-1">
                        <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500"></div>
                        <span>Loading metrics...</span>
                    </div>
                </div>
                <div id="metrics-timestamp" class="text-xs text-gray-400 mt-1 sm:mt-0 sm:ml-4 hidden">
                </div>
                <button id="refresh-metrics"
                        class="ml-3 text-blue-600 hover:text-blue-800 text-xs underline hidden"
                        onclick="loadMetrics()">
                    Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Fetch real-time metrics
async function loadMetrics() {
    try {
        const response = await fetch('/api/dashboard/metrics');
        const metrics = await response.json();

        const metricsContent = document.getElementById('metrics-content');
        const refreshButton = document.getElementById('refresh-metrics');
        const lastUpdated = new Date().toLocaleTimeString();

        // Focused metrics display (volume, quota, success rate)
        const quotaUsagePercent = Math.round((metrics.current_month_emails / metrics.monthly_email_limit) * 100);
        const quotaColor = quotaUsagePercent > 80 ? 'bg-red-500' : quotaUsagePercent > 60 ? 'bg-yellow-500' : 'bg-green-500';

        metricsContent.innerHTML = `
            <div class="flex items-center gap-1">
                <span class="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                <span><strong>${metrics.emails_processed_24h}</strong> emails (24h)</span>
            </div>
            <div class="flex items-center gap-1">
                <span class="inline-block w-2 h-2 ${quotaColor} rounded-full"></span>
                <span><strong>${metrics.current_month_emails}/${metrics.monthly_email_limit}</strong> quota (${quotaUsagePercent}%)</span>
            </div>
            <div class="flex items-center gap-1">
                <span class="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
                <span><strong>${metrics.success_rate}%</strong> success rate</span>
            </div>
        `;

        // Update timestamp separately
        const timestampElement = document.getElementById('metrics-timestamp');
        timestampElement.textContent = `Updated: ${lastUpdated}`;
        timestampElement.classList.remove('hidden');

        // Show refresh button
        refreshButton.classList.remove('hidden');

    } catch (error) {
        console.error('Failed to load metrics:', error);
        const metricsContent = document.getElementById('metrics-content');
        metricsContent.innerHTML = `
            <div class="flex items-center gap-2 text-red-600">
                <span>⚠</span>
                <span>Unable to load metrics</span>
            </div>
        `;
        document.getElementById('refresh-metrics').classList.remove('hidden');
    }
}

// Make loadMetrics available globally
window.loadMetrics = loadMetrics;

// Load metrics when page loads
document.addEventListener('DOMContentLoaded', loadMetrics);
</script>