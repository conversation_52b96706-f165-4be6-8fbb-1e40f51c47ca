<div class="border rounded-lg p-4">
    <div class="flex justify-between items-center">
        <h3 class="font-semibold">{{domainName}}</h3>
        {{#if isVerified}}
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Verified</span>
        {{else}}
            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">Pending</span>
        {{/if}}
    </div>
    
    {{#if aliases}}
        <div class="mt-2">
            <p class="text-sm text-gray-600">Aliases:</p>
            {{#each aliases}}
                <span class="inline-block bg-gray-100 px-2 py-1 rounded text-xs mr-1">{{localPart}}@{{../domainName}}</span>
            {{/each}}
        </div>
    {{/if}}
</div>